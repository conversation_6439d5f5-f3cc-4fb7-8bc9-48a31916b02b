<!-- Dashboard Scanner CTA Pulse Effect - Webflow Code Embed -->
<!-- Add this to your Dashboard page's custom code (Before </body> tag) -->

<style>
/* Dashboard Scanner CTA Pulse Effect */

/* Base styling for the CTA container */
.activate-scanner-container {
  position: relative;
  transition: all 0.3s ease;
  display: inline-block;
}

/* Active pulsing state for scanner CTA */
.activate-scanner-container.scanner-cta-active {
  animation: scannerCTAPulse 2.5s ease-in-out infinite;
}

/* Glow effect for the open-scanner div */
.activate-scanner-container.scanner-cta-active .open-scanner {
  box-shadow: 
    0 0 20px rgba(14, 248, 248, 0.4),
    0 0 40px rgba(14, 248, 248, 0.2),
    0 4px 20px rgba(0, 0, 0, 0.3);
  animation: ctaTextGlow 2.5s ease-in-out infinite;
}

/* Camera icon glow in CTA */
.activate-scanner-container.scanner-cta-active .icon_camera {
  filter: drop-shadow(0 0 10px rgba(255, 54, 88, 0.6));
  animation: ctaIconGlow 2.5s ease-in-out infinite;
}

/* Animated border around the entire CTA */
.activate-scanner-container.scanner-cta-active::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #0EF8F8, transparent, #FF3658, transparent, #0EF8F8);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: ctaBorderFlow 3s linear infinite;
  pointer-events: none;
  opacity: 0.6;
}

/* Subtle pulsing ring effect */
.activate-scanner-container.scanner-cta-active::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 2px solid rgba(14, 248, 248, 0.3);
  border-radius: inherit;
  animation: ctaPulseRing 2.5s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

/* Keyframe animations for CTA effects */
@keyframes scannerCTAPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
}

@keyframes ctaTextGlow {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(14, 248, 248, 0.4),
      0 0 40px rgba(14, 248, 248, 0.2),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(14, 248, 248, 0.6),
      0 0 60px rgba(14, 248, 248, 0.3),
      0 6px 25px rgba(0, 0, 0, 0.4);
  }
}

@keyframes ctaIconGlow {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(255, 54, 88, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 18px rgba(255, 54, 88, 0.9));
  }
}

@keyframes ctaBorderFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 400% 50%;
  }
}

@keyframes ctaPulseRing {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.6;
  }
}
</style>

<script>
// Dashboard Scanner CTA Control Functions

// Function to activate the pulse effect
function activateScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    ctaElement.classList.add('scanner-cta-active');
    console.log('✨ Scanner CTA pulse activated');
  }
}

// Function to deactivate the pulse effect
function deactivateScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    ctaElement.classList.remove('scanner-cta-active');
    console.log('⏹️ Scanner CTA pulse deactivated');
  }
}

// Function to toggle the pulse effect
function toggleScannerCTA() {
  const ctaElement = document.querySelector('.activate-scanner-container');
  if (ctaElement) {
    if (ctaElement.classList.contains('scanner-cta-active')) {
      deactivateScannerCTA();
    } else {
      activateScannerCTA();
    }
  }
}

// Auto-activate example (remove if not needed)
// Uncomment the lines below to automatically start the pulse effect
/*
document.addEventListener('DOMContentLoaded', function() {
  // Wait 2 seconds then activate the pulse
  setTimeout(() => {
    activateScannerCTA();
  }, 2000);
});
*/

// Example: Activate pulse when certain conditions are met
// You can customize this logic based on your needs
/*
document.addEventListener('DOMContentLoaded', function() {
  // Example: Activate pulse if user hasn't used scanner recently
  const lastScanTime = localStorage.getItem('lastScanTime');
  const now = Date.now();
  const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
  
  if (!lastScanTime || (now - parseInt(lastScanTime)) > oneHour) {
    // User hasn't scanned in over an hour, activate pulse
    setTimeout(() => {
      activateScannerCTA();
    }, 3000);
  }
});
*/
</script>

<!-- 
USAGE INSTRUCTIONS:

1. Copy this entire code block
2. In Webflow, go to your Dashboard page settings
3. Paste this code in the "Before </body> tag" section
4. Publish your site

Your HTML structure should be:
<a class="activate-scanner-container">
  <div class="open-scanner">
    <img class="icon_camera" src="your-icon.svg" />
    Open Scanner
  </div>
</a>

To control the effect with JavaScript:
- activateScannerCTA()   // Start pulsing
- deactivateScannerCTA() // Stop pulsing  
- toggleScannerCTA()     // Toggle on/off

Customization options:
- Change pulse speed: Modify "2.5s" in the animations
- Change colors: Modify the rgba() values
- Change intensity: Adjust the scale values (1.03) and glow sizes
-->
