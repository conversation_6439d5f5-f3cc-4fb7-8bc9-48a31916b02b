/* Dashboard Scanner CTA Pulse Effect - Standalone Code Embed */

/* Base styling for the CTA container */
.activate-scanner-container {
  position: relative;
  transition: all 0.3s ease;
  display: inline-block;
}

/* Active pulsing state for scanner CTA */
.activate-scanner-container.scanner-cta-active {
  animation: scannerCTAPulse 2.5s ease-in-out infinite;
}

/* Glow effect for the open-scanner div */
.activate-scanner-container.scanner-cta-active .open-scanner {
  box-shadow: 
    0 0 20px rgba(14, 248, 248, 0.4),
    0 0 40px rgba(14, 248, 248, 0.2),
    0 4px 20px rgba(0, 0, 0, 0.3);
  animation: ctaTextGlow 2.5s ease-in-out infinite;
}

/* Camera icon glow in CTA */
.activate-scanner-container.scanner-cta-active .icon_camera {
  filter: drop-shadow(0 0 10px rgba(255, 54, 88, 0.6));
  animation: ctaIconGlow 2.5s ease-in-out infinite;
}

/* Animated border around the entire CTA */
.activate-scanner-container.scanner-cta-active::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #0EF8F8, transparent, #FF3658, transparent, #0EF8F8);
  background-size: 400% 400%;
  border-radius: inherit;
  z-index: -1;
  animation: ctaBorderFlow 3s linear infinite;
  pointer-events: none;
  opacity: 0.6;
}

/* Subtle pulsing ring effect */
.activate-scanner-container.scanner-cta-active::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 2px solid rgba(14, 248, 248, 0.3);
  border-radius: inherit;
  animation: ctaPulseRing 2.5s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

/* Keyframe animations for CTA effects */
@keyframes scannerCTAPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
}

@keyframes ctaTextGlow {
  0%, 100% {
    box-shadow: 
      0 0 20px rgba(14, 248, 248, 0.4),
      0 0 40px rgba(14, 248, 248, 0.2),
      0 4px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 
      0 0 30px rgba(14, 248, 248, 0.6),
      0 0 60px rgba(14, 248, 248, 0.3),
      0 6px 25px rgba(0, 0, 0, 0.4);
  }
}

@keyframes ctaIconGlow {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(255, 54, 88, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 18px rgba(255, 54, 88, 0.9));
  }
}

@keyframes ctaBorderFlow {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 400% 50%;
  }
}

@keyframes ctaPulseRing {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.6;
  }
}
