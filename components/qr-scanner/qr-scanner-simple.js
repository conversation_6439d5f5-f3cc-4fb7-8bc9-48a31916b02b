/**
 * Simple QR Scanner for Webflow - Tablet Optimized
 * Handles any QR code content with automatic URL detection
 */

/**
 * Initialize the simple QR scanner
 */
function initSimpleQRScanner() {
  try {
    console.log('Initializing simple QR scanner');
    
    // Find scanner elements
    const container = document.querySelector('.qr-scanner-container');
    const videoElement = document.getElementById('qr-video');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchButton = document.querySelector('.qr-scanner-switch-camera');
    const flashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');
    
    if (!container || !videoElement) {
      console.error('QR scanner container or video element not found');
      return;
    }
    
    // Scanner state
    let html5QrCode = null;
    let isScanning = false;
    let currentCamera = 'environment'; // Start with back camera
    
    // Set up button content and event listeners
    setupButtons();
    
    // Handle scan result
    function handleScan(decodedText, decodedResult) {
      console.log('QR code scanned:', decodedText);
      
      // Clear any previous errors
      if (errorElement) {
        errorElement.style.display = 'none';
      }
      
      // Process the QR code content
      processQRContent(decodedText);
      
      // Dispatch custom event for external listeners
      const event = new CustomEvent('qr-code-scanned', { 
        detail: { content: decodedText, result: decodedResult },
        bubbles: true 
      });
      container.dispatchEvent(event);
    }
    
    // Process QR code content based on type
    function processQRContent(content) {
      if (!content) {
        showError('Empty QR code detected');
        return;
      }
      
      // Check if it's a URL
      if (isURL(content)) {
        showURLResult(content);
      } else {
        showTextResult(content);
      }
    }
    
    // Check if content is a URL
    function isURL(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        // Also check for common URL patterns without protocol
        return /^(www\.|[a-zA-Z0-9-]+\.[a-zA-Z]{2,})/.test(string);
      }
    }
    
    // Show URL result with action button
    function showURLResult(url) {
      if (!resultElement) return;
      
      // Ensure URL has protocol
      const fullURL = url.startsWith('http') ? url : `https://${url}`;
      
      resultElement.innerHTML = `
        <div class="qr-url-result">
          <div class="qr-result-label">🔗 Website Found:</div>
          <div class="qr-url-display">${url}</div>
          <button class="qr-visit-button" onclick="window.open('${fullURL}', '_blank')">
            🌐 Visit Website
          </button>
          <button class="qr-copy-button" onclick="copyToClipboard('${url}')">
            📋 Copy URL
          </button>
        </div>
      `;
      
      resultElement.style.display = 'block';
      showStatus('Website QR code detected!', 'success');
      
      // Auto-redirect option (uncomment if you want automatic redirects)
      // setTimeout(() => {
      //   if (confirm(`Open ${url}?`)) {
      //     window.open(fullURL, '_blank');
      //   }
      // }, 1000);
    }
    
    // Show text result
    function showTextResult(text) {
      if (!resultElement) return;
      
      resultElement.innerHTML = `
        <div class="qr-text-result">
          <div class="qr-result-label">📄 Text Content:</div>
          <div class="qr-text-display">${escapeHtml(text)}</div>
          <button class="qr-copy-button" onclick="copyToClipboard('${escapeHtml(text)}')">
            📋 Copy Text
          </button>
        </div>
      `;
      
      resultElement.style.display = 'block';
      showStatus('Text QR code scanned!', 'success');
    }
    
    // Escape HTML to prevent XSS
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }
    
    // Copy to clipboard function
    window.copyToClipboard = function(text) {
      navigator.clipboard.writeText(text).then(() => {
        showStatus('Copied to clipboard!', 'success');
      }).catch(err => {
        console.error('Failed to copy:', err);
        showStatus('Failed to copy to clipboard', 'error');
      });
    };
    
    // Start scanning
    function startScanning() {
      if (isScanning) return;
      
      try {
        if (!html5QrCode) {
          html5QrCode = new Html5Qrcode("qr-video");
        }
        
        // Clear previous results
        if (resultElement) {
          resultElement.style.display = 'none';
        }
        if (errorElement) {
          errorElement.style.display = 'none';
        }
        
        // Calculate QR box size optimized for landscape tablets
        const video = videoElement;
        const videoWidth = video.offsetWidth;
        const videoHeight = video.offsetHeight;
        const isLandscape = videoWidth > videoHeight;

        let qrboxSize;
        if (isLandscape) {
          // For landscape, use more of the available space
          const maxSize = Math.min(videoHeight * 0.8, videoWidth * 0.6);
          qrboxSize = Math.floor(maxSize);
        } else {
          // For portrait, use traditional sizing
          const minDimension = Math.min(videoWidth, videoHeight);
          qrboxSize = Math.floor(minDimension * 0.7);
        }

        // Ensure reasonable bounds
        qrboxSize = Math.max(200, Math.min(qrboxSize, 600));
        
        console.log(`QR box size: ${qrboxSize}px (${isLandscape ? 'landscape' : 'portrait'} mode)`);
        
        // Start scanning with optimized config
        const config = {
          fps: 10,
          qrbox: qrboxSize,
          aspectRatio: isLandscape ? 16/9 : 1.0
        };
        
        html5QrCode.start(
          { facingMode: currentCamera },
          config,
          handleScan,
          handleError
        ).then(() => {
          isScanning = true;
          updateButtonStates();
          showStatus('Scanner started - point camera at QR code', 'info');
        }).catch(err => {
          console.error('Failed to start scanning:', err);
          showError('Failed to start camera. Please check permissions.');
        });
        
      } catch (error) {
        console.error('Error starting scanner:', error);
        showError('Failed to initialize scanner');
      }
    }
    
    // Stop scanning
    function stopScanning() {
      if (!isScanning || !html5QrCode) return;
      
      html5QrCode.stop().then(() => {
        isScanning = false;
        updateButtonStates();
        showStatus('Scanner stopped', 'info');
      }).catch(err => {
        console.error('Error stopping scanner:', err);
      });
    }
    
    // Switch camera
    function switchCamera() {
      if (!isScanning) {
        showError('Start scanning first');
        return;
      }
      
      currentCamera = currentCamera === 'environment' ? 'user' : 'environment';
      
      // Restart with new camera
      stopScanning();
      setTimeout(() => {
        startScanning();
        showStatus(`Switched to ${currentCamera === 'environment' ? 'back' : 'front'} camera`, 'info');
      }, 500);
    }
    
    // Toggle flash (note: limited browser support)
    function toggleFlash() {
      showStatus('Flash control not available in web browsers', 'info');
    }
    
    // Set up buttons
    function setupButtons() {
      // Ensure buttons have content
      if (startButton && !startButton.textContent.trim()) {
        startButton.textContent = 'Start Scanning';
      }
      if (stopButton && !stopButton.textContent.trim()) {
        stopButton.textContent = 'Stop Scanning';
      }
      if (switchButton && !switchButton.textContent.trim()) {
        switchButton.textContent = 'Switch Camera';
      }
      if (flashButton && !flashButton.textContent.trim()) {
        flashButton.textContent = 'Toggle Flash';
      }
      
      // Add event listeners
      if (startButton) {
        startButton.addEventListener('click', startScanning);
      }
      if (stopButton) {
        stopButton.addEventListener('click', stopScanning);
      }
      if (switchButton) {
        switchButton.addEventListener('click', switchCamera);
      }
      if (flashButton) {
        flashButton.addEventListener('click', toggleFlash);
      }
      
      updateButtonStates();
    }
    
    // Update button states
    function updateButtonStates() {
      if (startButton) {
        startButton.disabled = isScanning;
        startButton.style.opacity = isScanning ? '0.5' : '1';
      }
      if (stopButton) {
        stopButton.disabled = !isScanning;
        stopButton.style.opacity = !isScanning ? '0.5' : '1';
      }
    }
    
    // Handle errors
    function handleError(error) {
      // Only log actual errors, not "No QR code found" messages
      if (!error.includes('No QR code found')) {
        console.error('QR scanner error:', error);
      }
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
      }
      console.error('QR Scanner Error:', message);
    }
    
    // Show status message
    function showStatus(message, type = 'info') {
      if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `qr-scanner-status ${type}`;
        statusElement.style.display = 'block';
        
        // Auto-hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 3000);
        }
      }
      console.log('QR Scanner Status:', message);
    }
    
    console.log('Simple QR scanner initialized successfully');
    
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initSimpleQRScanner);
} else {
  initSimpleQRScanner();
}
