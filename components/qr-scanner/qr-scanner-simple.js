/**
 * Simple QR Scanner for Webflow - Tablet Optimized
 * Handles any QR code content with automatic URL detection
 */

/**
 * Initialize the simple QR scanner
 */
function initSimpleQRScanner() {
  try {
    console.log('Initializing simple QR scanner');
    
    // Find scanner elements
    const container = document.querySelector('.qr-scanner-container');
    const videoElement = document.getElementById('qr-video');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchButton = document.querySelector('.qr-scanner-switch-camera');
    const flashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');

    if (!container || !videoElement) {
      console.error('QR scanner container or video element not found');
      return;
    }

    // Scanner state
    let html5QrCode = null;
    let isScanning = false;
    let currentCamera = 'environment'; // Start with back camera
    let lastScannedCode = null;
    let scanCooldown = false;
    const SCAN_COOLDOWN_TIME = 3000; // 3 seconds between same QR code scans

    // Set up button content and event listeners
    setupButtons();

    // Auto-start scanner after setup
    setTimeout(() => {
      startScanning();
    }, 500);
    
    // Handle scan result
    function handleScan(decodedText, decodedResult) {
      console.log('QR code scanned:', decodedText);
      
      // Clear any previous errors
      if (errorElement) {
        errorElement.style.display = 'none';
      }
      
      // Process the QR code content
      processQRContent(decodedText);
      
      // Dispatch custom event for external listeners
      const event = new CustomEvent('qr-code-scanned', { 
        detail: { content: decodedText, result: decodedResult },
        bubbles: true 
      });
      container.dispatchEvent(event);
    }
    
    // Process QR code content based on type with cooldown to prevent spam
    function processQRContent(content) {
      if (!content) {
        showError('Empty QR code detected');
        return;
      }

      // Check if this is the same code we just scanned
      if (lastScannedCode === content && scanCooldown) {
        console.log('Ignoring duplicate scan during cooldown period');
        return;
      }

      // If it's a new code or cooldown has expired, process it
      console.log('Processing new QR code:', content);
      lastScannedCode = content;
      scanCooldown = true;

      // Check if it's a URL
      if (isURL(content)) {
        showURLResult(content);
      } else {
        showTextResult(content);
      }

      // Reset cooldown after specified time
      setTimeout(() => {
        scanCooldown = false;
        console.log('Scan cooldown reset - ready for new scans');
      }, SCAN_COOLDOWN_TIME);
    }
    
    // Check if content is a URL
    function isURL(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        // Also check for common URL patterns without protocol
        return /^(www\.|[a-zA-Z0-9-]+\.[a-zA-Z]{2,})/.test(string);
      }
    }
    
    // Show URL result with iOS-style banner
    function showURLResult(url) {
      // Ensure URL has protocol
      const fullURL = url.startsWith('http') ? url : `https://${url}`;

      // Extract domain name only
      const domain = extractDomain(url);

      // Show iOS-style banner
      showIOSBanner(domain, fullURL);

      // Hide the regular result element
      if (resultElement) {
        resultElement.style.display = 'none';
      }

      showStatus('Website QR code detected!', 'success');
    }

    // Extract domain from URL
    function extractDomain(url) {
      try {
        // Handle URLs with or without protocol
        const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
        let domain = urlObj.hostname;

        // Remove www. prefix if present
        if (domain.startsWith('www.')) {
          domain = domain.substring(4);
        }

        return domain;
      } catch (error) {
        // Fallback for malformed URLs
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\?#]+)/);
        return match ? match[1] : url;
      }
    }

    // Show iOS-style banner notification
    function showIOSBanner(domain, fullURL) {
      // Remove any existing banner
      const existingBanner = document.querySelector('.ios-qr-banner');
      if (existingBanner) {
        // If banner already exists for same domain, don't recreate it
        const existingDomain = existingBanner.querySelector('.ios-banner-domain');
        if (existingDomain && existingDomain.textContent === domain) {
          console.log('Banner already showing for this domain');
          return;
        }
        existingBanner.remove();
      }

      // Create banner element
      const banner = document.createElement('div');
      banner.className = 'ios-qr-banner';
      banner.innerHTML = `
        <div class="ios-banner-content" onclick="openURL('${fullURL}')">
          <div class="ios-banner-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </svg>
          </div>
          <div class="ios-banner-text">
            <div class="ios-banner-domain">${domain}</div>
            <div class="ios-banner-subtitle">Open in browser</div>
          </div>
        </div>
        <button class="ios-banner-close" onclick="dismissBanner()" aria-label="Dismiss">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      `;

      // Add banner to container
      const container = document.querySelector('.qr-scanner-container');
      if (container) {
        container.appendChild(banner);

        // Animate in
        setTimeout(() => {
          banner.classList.add('ios-banner-visible');
        }, 10);

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
          dismissBanner();
        }, 10000);
      }
    }

    // Open URL function
    window.openURL = function(url) {
      window.open(url, '_blank');
      dismissBanner();
    };

    // Dismiss banner function
    window.dismissBanner = function() {
      const banner = document.querySelector('.ios-qr-banner');
      if (banner) {
        banner.classList.remove('ios-banner-visible');
        setTimeout(() => {
          banner.remove();
        }, 300);

        // Reset scan cooldown when user dismisses banner
        // This allows them to scan the same code again if needed
        scanCooldown = false;
        lastScannedCode = null;
        console.log('Banner dismissed - scan cooldown reset');
      }
    };
    
    // Show text result (minimal display)
    function showTextResult(text) {
      if (!resultElement) return;

      resultElement.innerHTML = `
        <div class="qr-text-result">
          <div class="qr-result-label">📄 Text Content:</div>
          <div class="qr-text-display">${escapeHtml(text)}</div>
          <button class="qr-copy-button" onclick="copyToClipboard('${escapeHtml(text)}')">
            📋 Copy Text
          </button>
        </div>
      `;

      resultElement.style.display = 'block';
      showStatus(`Text scanned: ${text.length > 30 ? text.substring(0, 30) + '...' : text}`, 'success');
    }
    
    // Escape HTML to prevent XSS
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }
    
    // Copy to clipboard function
    window.copyToClipboard = function(text) {
      navigator.clipboard.writeText(text).then(() => {
        showStatus('Copied to clipboard!', 'success');
      }).catch(err => {
        console.error('Failed to copy:', err);
        showStatus('Failed to copy to clipboard', 'error');
      });
    };
    
    // Start scanning
    function startScanning() {
      if (isScanning) return;
      
      try {
        if (!html5QrCode) {
          html5QrCode = new Html5Qrcode("qr-video");
        }
        
        // Clear previous results
        if (resultElement) {
          resultElement.style.display = 'none';
        }
        if (errorElement) {
          errorElement.style.display = 'none';
        }
        
        // Calculate QR box size optimized for landscape tablets
        const video = videoElement;
        const videoWidth = video.offsetWidth;
        const videoHeight = video.offsetHeight;
        const isLandscape = videoWidth > videoHeight;

        let qrboxSize;
        if (isLandscape) {
          // For landscape, use more of the available space
          const maxSize = Math.min(videoHeight * 0.8, videoWidth * 0.6);
          qrboxSize = Math.floor(maxSize);
        } else {
          // For portrait, use traditional sizing
          const minDimension = Math.min(videoWidth, videoHeight);
          qrboxSize = Math.floor(minDimension * 0.7);
        }

        // Ensure reasonable bounds
        qrboxSize = Math.max(200, Math.min(qrboxSize, 600));
        
        console.log(`QR box size: ${qrboxSize}px (${isLandscape ? 'landscape' : 'portrait'} mode)`);
        
        // Start scanning with optimized config
        const config = {
          fps: 10,
          qrbox: qrboxSize,
          aspectRatio: isLandscape ? 16/9 : 1.0
        };
        
        html5QrCode.start(
          { facingMode: currentCamera },
          config,
          handleScan,
          handleError
        ).then(() => {
          isScanning = true;
          updateButtonStates();
          showStatus('Scanner started - point camera at QR code', 'info');
        }).catch(err => {
          console.error('Failed to start scanning:', err);
          showError('Failed to start camera. Please check permissions.');
        });
        
      } catch (error) {
        console.error('Error starting scanner:', error);
        showError('Failed to initialize scanner');
      }
    }
    
    // Stop scanning
    function stopScanning() {
      if (!isScanning || !html5QrCode) return;
      
      html5QrCode.stop().then(() => {
        isScanning = false;
        updateButtonStates();
        showStatus('Scanner stopped', 'info');
      }).catch(err => {
        console.error('Error stopping scanner:', err);
      });
    }
    
    // Toggle flash (with proper torch support)
    async function toggleFlash() {
      if (!isScanning) {
        showError('Start scanning first to use flash');
        return;
      }

      try {
        // Get the current video stream
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: currentCamera }
        });

        const track = stream.getVideoTracks()[0];
        const capabilities = track.getCapabilities();

        if (capabilities.torch) {
          const settings = track.getSettings();
          const newTorchState = !settings.torch;

          await track.applyConstraints({
            advanced: [{ torch: newTorchState }]
          });

          showStatus(`Flash ${newTorchState ? 'on' : 'off'}`, 'info');
        } else {
          showStatus('Flash not supported on this device', 'info');
        }

        // Clean up the test stream
        stream.getTracks().forEach(track => track.stop());

      } catch (error) {
        console.error('Flash toggle error:', error);
        showStatus('Could not control flash', 'error');
      }
    }
    
    // Set up buttons
    function setupButtons() {
      // Ensure buttons have content
      if (startButton && !startButton.textContent.trim()) {
        startButton.textContent = 'Start Scanning';
      }
      if (stopButton && !stopButton.textContent.trim()) {
        stopButton.textContent = 'Stop Scanning';
      }

      // Hide switch camera button (not needed for this event)
      if (switchButton) {
        switchButton.style.display = 'none';
      }

      // Check if device supports flash and handle flash button
      checkFlashSupport();

      // Add event listeners
      if (startButton) {
        startButton.addEventListener('click', startScanning);
      }
      if (stopButton) {
        stopButton.addEventListener('click', stopScanning);
      }
      if (flashButton) {
        flashButton.addEventListener('click', toggleFlash);
      }

      updateButtonStates();
    }

    // Check if device supports flash
    async function checkFlashSupport() {
      try {
        // Try to get media devices to check capabilities
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        // Check if any video device supports torch (flash)
        let hasFlash = false;

        for (const device of videoDevices) {
          try {
            const stream = await navigator.mediaDevices.getUserMedia({
              video: { deviceId: device.deviceId }
            });

            const track = stream.getVideoTracks()[0];
            const capabilities = track.getCapabilities();

            if (capabilities.torch) {
              hasFlash = true;
            }

            // Stop the stream
            stream.getTracks().forEach(track => track.stop());

            if (hasFlash) break;
          } catch (error) {
            // Continue checking other devices
            console.log('Could not check device capabilities:', error);
          }
        }

        // Hide flash button if no flash support
        if (flashButton) {
          if (!hasFlash) {
            flashButton.style.display = 'none';
            console.log('Flash not supported on this device');
          } else {
            flashButton.textContent = 'Toggle Flash';
            console.log('Flash support detected');
          }
        }

      } catch (error) {
        console.log('Could not check flash support:', error);
        // Hide flash button if we can't determine support
        if (flashButton) {
          flashButton.style.display = 'none';
        }
      }
    }
    
    // Update button states
    function updateButtonStates() {
      if (startButton) {
        startButton.disabled = isScanning;
        startButton.style.opacity = isScanning ? '0.5' : '1';
      }
      if (stopButton) {
        stopButton.disabled = !isScanning;
        stopButton.style.opacity = !isScanning ? '0.5' : '1';
      }
    }
    
    // Handle errors
    function handleError(error) {
      // Only log actual errors, not "No QR code found" messages
      if (!error.includes('No QR code found')) {
        console.error('QR scanner error:', error);
      }
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
      }
      console.error('QR Scanner Error:', message);
    }
    
    // Show status message
    function showStatus(message, type = 'info') {
      if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = `qr-scanner-status ${type}`;
        statusElement.style.display = 'block';
        
        // Auto-hide after 3 seconds for success/info messages
        if (type === 'success' || type === 'info') {
          setTimeout(() => {
            statusElement.style.display = 'none';
          }, 3000);
        }
      }
      console.log('QR Scanner Status:', message);
    }
    
    console.log('Simple QR scanner initialized successfully');
    
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initSimpleQRScanner);
} else {
  initSimpleQRScanner();
}
