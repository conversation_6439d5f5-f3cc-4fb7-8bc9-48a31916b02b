/**
 * Simple QR Scanner for Webflow - Tablet Optimized
 * Handles any QR code content with automatic URL detection
 */

/**
 * Initialize the simple QR scanner
 */
function initSimpleQRScanner() {
  try {
    console.log('Initializing simple QR scanner');
    
    // Find scanner elements
    const container = document.querySelector('.qr-scanner-container');
    const videoElement = document.getElementById('qr-video');
    const startButton = document.querySelector('.qr-scanner-start-button');
    const stopButton = document.querySelector('.qr-scanner-stop-button');
    const switchButton = document.querySelector('.qr-scanner-switch-camera');
    const flashButton = document.querySelector('.qr-scanner-toggle-flash');
    const resultElement = document.querySelector('.qr-scanner-result');
    const errorElement = document.querySelector('.qr-scanner-error');
    const statusElement = document.querySelector('.qr-scanner-status');

    if (!container || !videoElement) {
      console.error('QR scanner container or video element not found');
      return;
    }

    // Scanner state
    let html5QrCode = null;
    let isScanning = false;
    let currentCamera = 'environment'; // Start with back camera
    let lastScannedCode = null;
    let scanCooldown = false;
    const SCAN_COOLDOWN_TIME = 3000; // 3 seconds between same QR code scans

    // Set up button content and event listeners
    setupButtons();

    // Auto-start scanner after setup
    setTimeout(() => {
      startScanning();
    }, 500);
    
    // Handle scan result
    function handleScan(decodedText, decodedResult) {
      // IMMEDIATE cooldown check - stop everything if duplicate
      if (lastScannedCode === decodedText && scanCooldown) {
        return; // Exit immediately, no processing at all
      }

      console.log('QR code scanned:', decodedText);

      // Clear any previous errors
      if (errorElement) {
        errorElement.style.display = 'none';
      }

      // Process the QR code content
      processQRContent(decodedText);

      // Dispatch custom event for external listeners
      const event = new CustomEvent('qr-code-scanned', {
        detail: { content: decodedText, result: decodedResult },
        bubbles: true
      });
      container.dispatchEvent(event);
    }
    
    // Process QR code content based on type
    function processQRContent(content) {
      if (!content) {
        showError('Empty QR code detected');
        return;
      }

      // Set cooldown for this content (cooldown check already happened in handleScan)
      console.log('Processing new QR code:', content);
      lastScannedCode = content;
      scanCooldown = true;

      // Check if it's a URL
      if (isURL(content)) {
        showURLResult(content);
      } else {
        showTextResult(content);
      }

      // Reset cooldown after specified time
      setTimeout(() => {
        scanCooldown = false;
        console.log('Scan cooldown reset - ready for new scans');
      }, SCAN_COOLDOWN_TIME);
    }
    
    // Check if content is a URL
    function isURL(string) {
      try {
        new URL(string);
        return true;
      } catch (_) {
        // Also check for common URL patterns without protocol
        return /^(www\.|[a-zA-Z0-9-]+\.[a-zA-Z]{2,})/.test(string);
      }
    }
    
    // Show URL result with iOS-style banner
    function showURLResult(url) {
      // Ensure URL has protocol
      const fullURL = url.startsWith('http') ? url : `https://${url}`;

      // Extract domain name only
      const domain = extractDomain(url);

      // Show iOS-style banner
      showIOSBanner(domain, fullURL);

      // Hide the regular result element
      if (resultElement) {
        resultElement.style.display = 'none';
      }

      // Trigger sci-fi target lock effect
      triggerTargetLockEffect();
    }

    // Extract domain from URL
    function extractDomain(url) {
      try {
        // Handle URLs with or without protocol
        const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
        let domain = urlObj.hostname;

        // Remove www. prefix if present
        if (domain.startsWith('www.')) {
          domain = domain.substring(4);
        }

        return domain;
      } catch (error) {
        // Fallback for malformed URLs
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\?#]+)/);
        return match ? match[1] : url;
      }
    }

    // Show iOS-style banner notification
    function showIOSBanner(domain, fullURL) {
      // Remove any existing banner
      const existingBanner = document.querySelector('.ios-qr-banner');
      if (existingBanner) {
        // If banner already exists for same domain, don't recreate it
        const existingDomain = existingBanner.querySelector('.ios-banner-domain');
        if (existingDomain && existingDomain.textContent === domain) {
          console.log('Banner already showing for this domain');
          return;
        }
        existingBanner.remove();
      }

      // Create banner element
      const banner = document.createElement('div');
      banner.className = 'ios-qr-banner';
      banner.innerHTML = `
        <div class="ios-banner-content" onclick="openURL('${fullURL}')">
          <div class="ios-banner-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </svg>
          </div>
          <div class="ios-banner-text">
            <div class="ios-banner-domain">${domain}</div>
            <div class="ios-banner-subtitle">Open in browser</div>
          </div>
        </div>
        <button class="ios-banner-close" onclick="dismissBanner()" aria-label="Dismiss">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      `;

      // Add banner to container
      const container = document.querySelector('.qr-scanner-container');
      if (container) {
        container.appendChild(banner);

        // Animate in
        setTimeout(() => {
          banner.classList.add('ios-banner-visible');
        }, 10);

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
          dismissBanner();
        }, 10000);
      }
    }

    // Open URL function - opens in same window for kiosk mode
    window.openURL = function(url) {
      window.location.href = url;
      // Note: dismissBanner() not needed since we're navigating away
    };

    // Sci-fi target lock effect with epic flares and sound
    function triggerTargetLockEffect() {
      // Play killer sound effect
      playTargetLockSound();

      // Create target lock overlay
      const targetLock = document.createElement('div');
      targetLock.className = 'target-lock-overlay';
      targetLock.innerHTML = `
        <div class="target-crosshair">
          <div class="crosshair-line crosshair-top"></div>
          <div class="crosshair-line crosshair-right"></div>
          <div class="crosshair-line crosshair-bottom"></div>
          <div class="crosshair-line crosshair-left"></div>
          <div class="target-center"></div>
        </div>
        <div class="scan-pulse"></div>
        <div class="energy-flare flare-1"></div>
        <div class="energy-flare flare-2"></div>
        <div class="energy-flare flare-3"></div>
        <div class="energy-flare flare-4"></div>
        <div class="energy-flare flare-5"></div>
        <div class="energy-flare flare-6"></div>
      `;

      container.appendChild(targetLock);

      // Trigger animation
      setTimeout(() => {
        targetLock.classList.add('target-lock-active');
      }, 50);

      // Remove after animation
      setTimeout(() => {
        targetLock.classList.add('target-lock-fade');
        setTimeout(() => {
          if (targetLock.parentNode) {
            targetLock.remove();
          }
        }, 500);
      }, 1500);
    }

    // Play epic Dune-style laser beam sound effect
    function playTargetLockSound() {
      try {
        // Create audio context for synthesized sound
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Create epic Dune laser beam sound - deep, resonant, otherworldly
        const duration = 1.5;
        const sampleRate = audioContext.sampleRate;
        const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
          const t = i / sampleRate;

          // Dune-style laser beam components
          const deepBass = Math.sin(2 * Math.PI * 60 * t) * Math.exp(-t * 2); // Deep bass foundation
          const harmonicBass = Math.sin(2 * Math.PI * 120 * t) * Math.exp(-t * 2.5); // Bass harmonic
          const powerSurge = Math.sin(2 * Math.PI * (80 + 40 * Math.sin(t * 8)) * t) * Math.exp(-t * 1.5); // Power modulation

          // Laser beam charge-up sweep (like Dune weapons)
          const chargeFreq = 150 + (300 * t * t); // Exponential frequency rise
          const chargeSweep = Math.sin(2 * Math.PI * chargeFreq * t) * Math.exp(-t * 3);

          // Resonant harmonics (that otherworldly Dune sound)
          const harmonic1 = Math.sin(2 * Math.PI * 180 * t) * Math.exp(-t * 2.2);
          const harmonic2 = Math.sin(2 * Math.PI * 240 * t) * Math.exp(-t * 2.8);

          // Energy discharge (the "THOOM" effect)
          const discharge = Math.sin(2 * Math.PI * (400 - 350 * t) * t) * Math.exp(-t * 4);

          // Atmospheric distortion (like air being displaced)
          const distortion = (Math.random() - 0.5) * 0.05 * Math.exp(-t * 5);

          // Sub-bass rumble (that chest-thumping low end)
          const subBass = Math.sin(2 * Math.PI * 40 * t) * Math.exp(-t * 1.8);

          // Combine all layers for epic Dune laser sound
          data[i] = (
            deepBass * 0.35 +           // Deep foundation
            harmonicBass * 0.25 +       // Bass harmonic
            powerSurge * 0.2 +          // Power modulation
            chargeSweep * 0.15 +        // Charge sweep
            harmonic1 * 0.1 +           // Resonant harmonic 1
            harmonic2 * 0.08 +          // Resonant harmonic 2
            discharge * 0.12 +          // Energy discharge
            subBass * 0.3 +             // Sub-bass rumble
            distortion                  // Atmospheric distortion
          ) * 0.4; // Master volume
        }

        // Add reverb/echo effect for that cavernous Dune sound
        const convolver = audioContext.createConvolver();
        const impulseBuffer = audioContext.createBuffer(1, sampleRate * 0.5, sampleRate);
        const impulseData = impulseBuffer.getChannelData(0);

        // Create reverb impulse (simulates vast space)
        for (let i = 0; i < impulseBuffer.length; i++) {
          const decay = Math.exp(-i / (sampleRate * 0.1));
          impulseData[i] = (Math.random() - 0.5) * decay * 0.3;
        }

        convolver.buffer = impulseBuffer;

        // Create and connect audio nodes
        const source = audioContext.createBufferSource();
        const gainNode = audioContext.createGain();

        source.buffer = buffer;

        // Connect: source -> gain -> convolver -> destination (for reverb)
        source.connect(gainNode);
        gainNode.connect(convolver);
        convolver.connect(audioContext.destination);

        // Also connect direct path for punch
        gainNode.connect(audioContext.destination);

        // Set gain curve for dramatic impact
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(1, audioContext.currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.1, audioContext.currentTime + duration);

        source.start();

      } catch (error) {
        console.log('Audio not available:', error);
        // Fallback - no sound, but effect still works
      }
    }

    // Dismiss banner function
    window.dismissBanner = function() {
      const banner = document.querySelector('.ios-qr-banner');
      if (banner) {
        banner.classList.remove('ios-banner-visible');
        setTimeout(() => {
          banner.remove();
        }, 300);

        // Reset scan cooldown when user dismisses banner
        // This allows them to scan the same code again if needed
        scanCooldown = false;
        lastScannedCode = null;
        console.log('Banner dismissed - scan cooldown reset');
      }
    };
    
    // Show text result (completely silent - no display)
    function showTextResult(text) {
      // Just trigger the sci-fi effect, no text display
      triggerTargetLockEffect();

      // Log to console for debugging if needed
      console.log('Text QR code scanned:', text);
    }
    
    // Escape HTML to prevent XSS
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }
    
    // Copy to clipboard function
    window.copyToClipboard = function(text) {
      navigator.clipboard.writeText(text).then(() => {
        showFloatingNotification('Copied to clipboard!', 'success');
      }).catch(err => {
        console.error('Failed to copy:', err);
        showFloatingNotification('Failed to copy to clipboard', 'error');
      });
    };
    
    // Start scanning
    function startScanning() {
      if (isScanning) return;
      
      try {
        if (!html5QrCode) {
          html5QrCode = new Html5Qrcode("qr-video");
        }
        
        // Clear previous results
        if (resultElement) {
          resultElement.style.display = 'none';
        }
        if (errorElement) {
          errorElement.style.display = 'none';
        }
        
        // Calculate QR box size optimized for landscape tablets
        const video = videoElement;
        const videoWidth = video.offsetWidth;
        const videoHeight = video.offsetHeight;
        const isLandscape = videoWidth > videoHeight;

        let qrboxSize;
        if (isLandscape) {
          // For landscape, use more of the available space
          const maxSize = Math.min(videoHeight * 0.8, videoWidth * 0.6);
          qrboxSize = Math.floor(maxSize);
        } else {
          // For portrait, use traditional sizing
          const minDimension = Math.min(videoWidth, videoHeight);
          qrboxSize = Math.floor(minDimension * 0.7);
        }

        // Ensure reasonable bounds
        qrboxSize = Math.max(200, Math.min(qrboxSize, 600));
        
        console.log(`QR box size: ${qrboxSize}px (${isLandscape ? 'landscape' : 'portrait'} mode)`);
        
        // Start scanning with optimized config
        const config = {
          fps: 10,
          qrbox: qrboxSize,
          aspectRatio: isLandscape ? 16/9 : 1.0
        };
        
        html5QrCode.start(
          { facingMode: currentCamera },
          config,
          handleScan,
          handleError
        ).then(() => {
          isScanning = true;
          updateButtonStates();
          // Scanner started - no notification needed
        }).catch(err => {
          console.error('Failed to start scanning:', err);
          showError('Failed to start camera. Please check permissions.');
        });
        
      } catch (error) {
        console.error('Error starting scanner:', error);
        showError('Failed to initialize scanner');
      }
    }
    
    // Stop scanning
    function stopScanning() {
      if (!isScanning || !html5QrCode) return;
      
      html5QrCode.stop().then(() => {
        isScanning = false;
        updateButtonStates();
        // Scanner stopped - no notification needed
      }).catch(err => {
        console.error('Error stopping scanner:', err);
      });
    }
    
    // Toggle flash (with proper torch support)
    async function toggleFlash() {
      if (!isScanning) {
        showError('Start scanning first to use flash');
        return;
      }

      try {
        // Get the current video stream
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: currentCamera }
        });

        const track = stream.getVideoTracks()[0];
        const capabilities = track.getCapabilities();

        if (capabilities.torch) {
          const settings = track.getSettings();
          const newTorchState = !settings.torch;

          await track.applyConstraints({
            advanced: [{ torch: newTorchState }]
          });

          showFloatingNotification(`Flash ${newTorchState ? 'on' : 'off'}`, 'info');
        } else {
          showFloatingNotification('Flash not supported on this device', 'info');
        }

        // Clean up the test stream
        stream.getTracks().forEach(track => track.stop());

      } catch (error) {
        console.error('Flash toggle error:', error);
        showFloatingNotification('Could not control flash', 'error');
      }
    }
    
    // Set up buttons
    function setupButtons() {
      // Ensure buttons have content
      if (startButton && !startButton.textContent.trim()) {
        startButton.textContent = 'Start Scanning';
      }
      if (stopButton && !stopButton.textContent.trim()) {
        stopButton.textContent = 'Stop Scanning';
      }

      // Hide switch camera button (not needed for this event)
      if (switchButton) {
        switchButton.style.display = 'none';
      }

      // Hide flash button - Surface tablets don't have flash
      if (flashButton) {
        flashButton.style.display = 'none';
      }

      // Add event listeners
      if (startButton) {
        startButton.addEventListener('click', startScanning);
      }
      if (stopButton) {
        stopButton.addEventListener('click', stopScanning);
      }
      if (flashButton) {
        flashButton.addEventListener('click', toggleFlash);
      }

      updateButtonStates();
    }

    // Flash detection removed for faster loading on Surface tablets
    
    // Update button states
    function updateButtonStates() {
      if (startButton) {
        startButton.disabled = isScanning;
        startButton.style.opacity = isScanning ? '0.5' : '1';
      }
      if (stopButton) {
        stopButton.disabled = !isScanning;
        stopButton.style.opacity = !isScanning ? '0.5' : '1';
      }
    }
    
    // Handle errors
    function handleError(error) {
      // Only log actual errors, not "No QR code found" messages
      if (!error.includes('No QR code found')) {
        console.error('QR scanner error:', error);
      }
    }
    
    // Show error message
    function showError(message) {
      if (errorElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
      }
      console.error('QR Scanner Error:', message);
    }
    
    // Show floating notification that doesn't affect layout
    function showFloatingNotification(message, type = 'info') {
      // Remove any existing floating notifications
      const existingNotifications = document.querySelectorAll('.floating-notification');
      existingNotifications.forEach(notification => notification.remove());

      // Create floating notification
      const notification = document.createElement('div');
      notification.className = `floating-notification floating-${type}`;
      notification.textContent = message;

      // Add to container
      container.appendChild(notification);

      // Trigger animation
      setTimeout(() => {
        notification.classList.add('floating-visible');
      }, 50);

      // Auto-hide after 2 seconds
      setTimeout(() => {
        notification.classList.add('floating-fade');
        setTimeout(() => {
          if (notification.parentNode) {
            notification.remove();
          }
        }, 300);
      }, 2000);

      console.log('QR Scanner Notification:', message);
    }

    // Legacy function for compatibility (now uses floating notifications)
    function showStatus(message, type = 'info') {
      showFloatingNotification(message, type);
    }
    
    console.log('Simple QR scanner initialized successfully');
    
  } catch (error) {
    console.error('Failed to initialize QR scanner:', error);
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initSimpleQRScanner);
} else {
  initSimpleQRScanner();
}
