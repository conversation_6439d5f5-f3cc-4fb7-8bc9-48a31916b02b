<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>Simple QR Scanner - Tablet Optimized</title>
  
  <!-- HTML5 QR Code library -->
  <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
  
  <!-- QR Scanner styles -->
  <link rel="stylesheet" href="qr-scanner-styles.css">
  
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f7;
      overflow: hidden;
      height: 100vh;
      width: 100vw;
    }
    
    .main-content {
      height: 100vh;
      width: 100vw;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 16px 24px;
      text-align: center;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      flex-shrink: 0;
    }
    
    .header h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 700;
    }
    
    .header p {
      margin: 8px 0 0 0;
      opacity: 0.9;
      font-size: 14px;
    }
    
    .scanner-wrapper {
      flex: 1;
      display: flex;
      padding: 20px;
      background-color: #f5f5f7;
    }
    
    .qr-scanner-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      margin: 0;
      flex: 1;
      position: relative; /* For iOS banner positioning */
    }
    
    /* Orientation message */
    .orientation-message {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #000;
      color: white;
      text-align: center;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 9999;
      font-size: 24px;
    }
    
    @media screen and (orientation: portrait) {
      .orientation-message {
        display: flex;
      }
      .main-content {
        display: none;
      }
    }
  </style>
</head>
<body>
  <!-- Orientation message -->
  <div class="orientation-message">
    <div>
      <h2>📱 ➡️ 📱</h2>
      <p>Please rotate your device to landscape mode</p>
      <p style="font-size: 16px; opacity: 0.8; margin-top: 20px;">
        This QR scanner works best in landscape orientation
      </p>
    </div>
  </div>

  <!-- Main content -->
  <div class="main-content">
    <!-- Header -->
    <div class="header">
      <h1>📱 Simple QR Scanner</h1>
      <p>Scanner starts automatically - URLs show iOS-style banner at top</p>
    </div>

    <!-- Scanner wrapper -->
    <div class="scanner-wrapper">
      <!-- QR Scanner Container -->
      <div class="qr-scanner-container">
        <!-- Video element for camera feed -->
        <div class="qr-scanner-video" id="qr-video"></div>
        
        <!-- Control buttons -->
        <div class="qr-scanner-buttons">
          <button class="qr-scanner-start-button">Start Scanning</button>
          <button class="qr-scanner-stop-button">Stop Scanning</button>
          <button class="qr-scanner-switch-camera">Switch Camera</button>
          <button class="qr-scanner-toggle-flash">Toggle Flash</button>
        </div>
        
        <!-- Result display -->
        <div class="qr-scanner-result"></div>
        
        <!-- Error display -->
        <div class="qr-scanner-error"></div>
        
        <!-- Status display -->
        <div class="qr-scanner-status"></div>
      </div>
    </div>
  </div>

  <!-- Simple QR Scanner script -->
  <script src="qr-scanner-simple.js"></script>
  
  <script>
    // Additional event listeners for demo
    document.addEventListener('DOMContentLoaded', function() {
      const container = document.querySelector('.qr-scanner-container');
      
      // Listen for QR code scans
      if (container) {
        container.addEventListener('qr-code-scanned', function(event) {
          const { content, result } = event.detail;
          console.log('QR code detected:', content);
          
          // You can add custom logic here
          // For example, send to analytics, log to server, etc.
        });
      }
      
      // Prevent zoom on double tap (iOS Safari)
      let lastTouchEnd = 0;
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
      
      // Lock orientation to landscape if possible
      if (screen.orientation && screen.orientation.lock) {
        screen.orientation.lock('landscape').catch(err => {
          console.log('Orientation lock not supported:', err);
        });
      }
    });
  </script>
</body>
</html>
