/* /// QR Scanner Styling - Optimized for Tablets in Landscape /// */

/* QR Scanner Container - Optimized for horizontal layout */
.qr-scanner-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
}

/* Video Element - Optimized for landscape tablets */
.qr-scanner-video {
  width: 100%;
  height: 100%;
  flex: 1;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  object-fit: cover;
  /* Ensure video fills available space in landscape */
  min-height: 400px;
  max-height: calc(100vh - 120px); /* Leave space for buttons */
}

/* Canvas Element */
.qr-scanner-canvas {
  display: none; /* Hidden by default */
  position: absolute;
  top: 0;
  left: 0;
}

/* Button Styling - Enhanced for tablet touch interaction */
.qr-scanner-start-button,
.qr-scanner-stop-button,
.qr-scanner-switch-camera,
.qr-scanner-toggle-flash {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  margin: 8px;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  min-height: 48px;
  min-width: 120px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  position: relative;
}

/* Add button content and icons */
.qr-scanner-start-button::before {
  content: "▶ ";
  margin-right: 8px;
}

.qr-scanner-stop-button::before {
  content: "⏹ ";
  margin-right: 8px;
}

.qr-scanner-switch-camera {
  display: none; /* Hidden by default - not needed for this event */
}

.qr-scanner-switch-camera::before {
  content: "🔄 ";
  margin-right: 8px;
}

.qr-scanner-toggle-flash::before {
  content: "💡 ";
  margin-right: 8px;
}

/* Flash button will be hidden via JavaScript if device doesn't support it */

/* Button hover states */
.qr-scanner-start-button:hover,
.qr-scanner-switch-camera:hover,
.qr-scanner-toggle-flash:hover {
  background-color: #0056CC;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.4);
}

.qr-scanner-stop-button {
  background-color: #FF3B30;
  display: none; /* Hidden by default */
  box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
}

.qr-scanner-stop-button:hover {
  background-color: #D70015;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
}

/* Button Container - Hidden since auto-start removes need for user actions */
.qr-scanner-buttons {
  display: none; /* Hidden - no user actions needed with auto-start */
}

/* Result Display */
.qr-scanner-result {
  background-color: rgba(52, 199, 89, 0.1);
  border: 2px solid #34C759;
  color: #1D4ED8;
  padding: 16px;
  border-radius: 12px;
  margin: 16px;
  font-weight: 600;
  text-align: center;
  display: none;
  animation: slideIn 0.3s ease-out;
}

.qr-scanner-result.scan-success {
  animation: pulse 0.6s ease-in-out;
}

/* URL and Text Result Styling */
.qr-url-result,
.qr-text-result {
  text-align: left;
}

.qr-result-label {
  font-size: 14px;
  font-weight: 700;
  color: #007AFF;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.qr-url-display,
.qr-text-display {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 14px;
  margin: 8px 0 16px 0;
  word-break: break-all;
  border: 1px solid #e9ecef;
}

.qr-visit-button,
.qr-copy-button {
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin: 4px 8px 4px 0;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.qr-visit-button:hover,
.qr-copy-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.4);
}

.qr-copy-button {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.qr-copy-button:hover {
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.4);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* iOS-style QR Banner - Bottom Centered Pill */
.ios-qr-banner {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  background: rgba(255, 204, 0, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 50px; /* Fully rounded pill shape */
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  user-select: none;
  max-width: 320px;
  min-width: 280px;
}

.ios-qr-banner.ios-banner-visible {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.ios-banner-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.ios-banner-icon {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.8);
}

.ios-banner-text {
  flex: 1;
  min-width: 0;
}

.ios-banner-domain {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ios-banner-subtitle {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.6);
  font-weight: 500;
}

.ios-banner-close {
  width: 28px;
  height: 28px;
  background: rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 12px;
  flex-shrink: 0;
  color: rgba(0, 0, 0, 0.6);
  transition: all 0.2s ease;
}

.ios-banner-close:hover {
  background: rgba(0, 0, 0, 0.15);
  color: rgba(0, 0, 0, 0.8);
}

.ios-banner-close:active {
  transform: scale(0.95);
}

/* Hover effect for the banner */
.ios-qr-banner:hover {
  background: rgba(255, 204, 0, 1);
  transform: translateX(-50%) translateY(0) scale(1.05);
}

/* Sci-Fi Target Lock Effect */
.target-lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.target-lock-overlay.target-lock-active {
  opacity: 1;
}

.target-lock-overlay.target-lock-fade {
  opacity: 0;
  transition: opacity 0.5s ease;
}

.target-crosshair {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
}

.crosshair-line {
  position: absolute;
  background: #00ff41;
  box-shadow: 0 0 10px #00ff41, 0 0 20px #00ff41;
  animation: crosshairPulse 0.8s ease-in-out;
}

.crosshair-top {
  top: 0;
  left: 50%;
  width: 2px;
  height: 30px;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.crosshair-right {
  top: 50%;
  right: 0;
  width: 30px;
  height: 2px;
  transform: translateY(-50%);
  animation-delay: 0.1s;
}

.crosshair-bottom {
  bottom: 0;
  left: 50%;
  width: 2px;
  height: 30px;
  transform: translateX(-50%);
  animation-delay: 0.2s;
}

.crosshair-left {
  top: 50%;
  left: 0;
  width: 30px;
  height: 2px;
  transform: translateY(-50%);
  animation-delay: 0.3s;
}

.target-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #ff0040;
  border-radius: 50%;
  box-shadow: 0 0 15px #ff0040, 0 0 30px #ff0040;
  animation: centerPulse 0.6s ease-in-out infinite alternate;
  animation-delay: 0.4s;
}

.scan-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid #00ff41;
  border-radius: 50%;
  opacity: 0;
  animation: scanPulse 1.2s ease-out;
}

.target-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, calc(-50% + 80px));
  color: #00ff41;
  font-family: 'Courier New', monospace;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 0 10px #00ff41;
  letter-spacing: 2px;
  opacity: 0;
  animation: textAppear 0.5s ease-out 0.6s forwards;
}

@keyframes crosshairPulse {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes centerPulse {
  0% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.3);
  }
}

@keyframes scanPulse {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  30% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes textAppear {
  0% {
    opacity: 0;
    transform: translate(-50%, calc(-50% + 100px));
  }
  100% {
    opacity: 1;
    transform: translate(-50%, calc(-50% + 80px));
  }
}

/* Floating Notifications - No Layout Impact */
.floating-notification {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(-50px);
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  z-index: 1001;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  pointer-events: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.floating-notification.floating-visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.floating-notification.floating-fade {
  opacity: 0;
  transform: translateX(-50%) translateY(-30px);
}

.floating-notification.floating-success {
  background: rgba(0, 255, 65, 0.15);
  border-color: rgba(0, 255, 65, 0.3);
  color: #00ff41;
}

.floating-notification.floating-error {
  background: rgba(255, 0, 64, 0.15);
  border-color: rgba(255, 0, 64, 0.3);
  color: #ff0040;
}

.floating-notification.floating-info {
  background: rgba(0, 150, 255, 0.15);
  border-color: rgba(0, 150, 255, 0.3);
  color: #0096ff;
}

/* Animation for banner appearance */
@keyframes bannerSlideIn {
  from {
    transform: translateY(-100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Error Display */
.qr-scanner-error {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  display: none; /* Hidden by default */
}

/* Status Display */
.qr-scanner-status {
  margin-top: 10px;
  padding: 10px;
  border-radius: 5px;
  text-align: center;
  display: none; /* Hidden by default */
}

.status-info {
  background-color: #cce5ff;
  color: #004085;
}

.status-success {
  background-color: #d4edda;
  color: #155724;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
}

/* Offline Indicator */
.qr-scanner-offline-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 5px;
  font-size: 12px;
  display: none; /* Hidden by default */
}

/* Animation for successful scan */
@keyframes scanSuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.scan-success {
  animation: scanSuccess 0.5s ease;
}

/* Scanning active state */
.qr-scanner-container.scanning .qr-scanner-video {
  border: 2px solid #28a745;
}

/* Scan region overlay */
.qr-scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.qr-scanner-overlay::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 70%;
  height: 70%;
  transform: translate(-50%, -50%);
  border: 2px solid #28a745;
  border-radius: 10px;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.3);
  clip-path: inset(0 -100vmax -100vmax 0);
}

/* Flash on state */
.qr-scanner-toggle-flash.flash-on {
  background-color: #ffc107;
  color: #212529;
}

/* Responsive Design - Optimized for Target Devices */

/* Microsoft Surface Go 4 (1920x1280 landscape) */
@media screen and (min-width: 1200px) and (max-width: 1920px) and (min-height: 1000px) and (max-height: 1280px) {
  .qr-scanner-container {
    padding: 20px;
    max-height: 1240px; /* Leave space for browser UI */
  }

  .qr-scanner-video {
    min-height: 600px;
    max-height: calc(100vh - 140px);
  }

  .qr-scanner-buttons {
    padding: 20px;
    gap: 16px;
  }

  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    min-width: 140px;
    padding: 14px 24px;
    font-size: 18px;
  }
}

/* iPad 11-inch (2360x1640 landscape) */
@media screen and (min-width: 1600px) and (max-width: 2360px) and (min-height: 1200px) and (max-height: 1640px) {
  .qr-scanner-container {
    padding: 24px;
    max-height: 1600px;
  }

  .qr-scanner-video {
    min-height: 700px;
    max-height: calc(100vh - 160px);
  }

  .qr-scanner-buttons {
    padding: 24px;
    gap: 20px;
  }

  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    min-width: 160px;
    padding: 16px 28px;
    font-size: 20px;
  }
}

/* General landscape orientation optimization */
@media screen and (orientation: landscape) and (min-width: 1024px) {
  .qr-scanner-container {
    height: 100vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .qr-scanner-video {
    flex: 1;
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  .qr-scanner-buttons {
    flex-shrink: 0;
    order: 2;
  }
}

/* Fallback for smaller screens */
@media (max-width: 1023px) {
  .qr-scanner-container {
    padding: 16px;
    height: auto;
  }

  .qr-scanner-video {
    min-height: 300px;
    max-height: 60vh;
  }

  .qr-scanner-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

  .qr-scanner-start-button,
  .qr-scanner-stop-button,
  .qr-scanner-switch-camera,
  .qr-scanner-toggle-flash {
    min-width: 100px;
    padding: 10px 16px;
    font-size: 14px;
  }
}


/* Additional optimizations for QR scanning performance */
.qr-scanner-container.scanning .qr-scanner-video {
  border: 3px solid #34C759;
  box-shadow: 0 0 20px rgba(52, 199, 89, 0.3);
}

/* Flash button active state */
.qr-scanner-toggle-flash.flash-on {
  background-color: #FF9500;
  color: white;
  box-shadow: 0 2px 8px rgba(255, 149, 0, 0.4);
}

.qr-scanner-toggle-flash.flash-on:hover {
  background-color: #FF8C00;
  box-shadow: 0 4px 12px rgba(255, 149, 0, 0.5);
}

/* Ensure proper aspect ratio for QR scanning */
.qr-scanner-video video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}