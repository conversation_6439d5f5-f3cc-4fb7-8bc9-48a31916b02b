<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>iOS-Style QR Banner Demo</title>
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f7;
      min-height: 100vh;
    }
    
    .demo-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 16px;
      padding: 30px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    h1 {
      text-align: center;
      color: #1d1d1f;
      margin-bottom: 30px;
    }
    
    .demo-section {
      margin-bottom: 40px;
      padding: 20px;
      border: 2px solid #e5e5e7;
      border-radius: 12px;
      position: relative;
    }
    
    .demo-section h3 {
      color: #007AFF;
      margin-top: 0;
    }
    
    button {
      background-color: #007AFF;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      margin: 5px;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #0056CC;
    }
    
    .qr-display {
      text-align: center;
      margin: 20px 0;
    }
    
    canvas {
      border: 2px solid #e5e5e7;
      border-radius: 8px;
    }
    
    .preview-area {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      margin: 20px 0;
      min-height: 200px;
      position: relative;
      border: 2px dashed #dee2e6;
    }
    
    .preview-label {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      margin-bottom: 20px;
    }
    
    /* Include the iOS banner styles */
    .ios-qr-banner {
      position: absolute;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%) translateY(100px);
      background: rgba(255, 204, 0, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 50px; /* Fully rounded pill shape */
      padding: 16px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      z-index: 1000;
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      cursor: pointer;
      user-select: none;
      max-width: 320px;
      min-width: 280px;
    }

    .ios-qr-banner.ios-banner-visible {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }

    .ios-banner-content {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
    }

    .ios-banner-icon {
      width: 32px;
      height: 32px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.8);
    }

    .ios-banner-text {
      flex: 1;
      min-width: 0;
    }

    .ios-banner-domain {
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.9);
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .ios-banner-subtitle {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.6);
      font-weight: 500;
    }

    .ios-banner-close {
      width: 28px;
      height: 28px;
      background: rgba(0, 0, 0, 0.1);
      border: none;
      border-radius: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 12px;
      flex-shrink: 0;
      color: rgba(0, 0, 0, 0.6);
      transition: all 0.2s ease;
    }

    .ios-banner-close:hover {
      background: rgba(0, 0, 0, 0.15);
      color: rgba(0, 0, 0, 0.8);
    }

    .ios-qr-banner:hover {
      background: rgba(255, 204, 0, 1);
      transform: translateX(-50%) translateY(0) scale(1.05);
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1>📱 iOS-Style QR Banner Demo</h1>
    
    <div class="demo-section">
      <h3>Preview Area</h3>
      <p>This shows how the iOS-style banner will appear when scanning URL QR codes:</p>
      
      <div class="preview-area" id="preview-area">
        <div class="preview-label">iOS pill banner will appear at the bottom when you test URLs below</div>
      </div>
      
      <button onclick="clearBanner()">Clear Banner</button>
    </div>

    <div class="demo-section">
      <h3>Test Different URLs</h3>
      <p>Click these buttons to see how different URLs appear in the iOS banner:</p>
      
      <button onclick="testURL('https://apple.com')">Test apple.com</button>
      <button onclick="testURL('https://www.google.com/search?q=test')">Test google.com (with path)</button>
      <button onclick="testURL('github.com')">Test github.com (no protocol)</button>
      <button onclick="testURL('https://very-long-domain-name-example.com/very/long/path')">Test Long Domain</button>
      
      <div class="qr-display" id="qr-display"></div>
    </div>

    <div class="demo-section">
      <h3>How It Works</h3>
      <ul>
        <li><strong>Yellow pill banner</strong> slides up from the bottom when URL is detected</li>
        <li><strong>Centered pill shape</strong> with fully rounded borders (like Apple's design)</li>
        <li><strong>Domain only</strong> is shown (e.g., "apple.com" not "https://www.apple.com/path")</li>
        <li><strong>Click banner</strong> to open the URL in a new tab</li>
        <li><strong>X button</strong> on the right to dismiss</li>
        <li><strong>Auto-dismisses</strong> after 10 seconds</li>
        <li><strong>Checkmark icon</strong> on the left (like iOS)</li>
        <li><strong>Maximized scanner space</strong> - no button bar needed</li>
      </ul>
    </div>
  </div>

  <script>
    // Extract domain from URL (same logic as in the scanner)
    function extractDomain(url) {
      try {
        const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
        let domain = urlObj.hostname;
        
        if (domain.startsWith('www.')) {
          domain = domain.substring(4);
        }
        
        return domain;
      } catch (error) {
        const match = url.match(/(?:https?:\/\/)?(?:www\.)?([^\/\?#]+)/);
        return match ? match[1] : url;
      }
    }

    // Show iOS-style banner
    function showIOSBanner(domain, fullURL) {
      const previewArea = document.getElementById('preview-area');
      
      // Remove existing banner
      const existingBanner = previewArea.querySelector('.ios-qr-banner');
      if (existingBanner) {
        existingBanner.remove();
      }
      
      // Create banner
      const banner = document.createElement('div');
      banner.className = 'ios-qr-banner';
      banner.innerHTML = `
        <div class="ios-banner-content" onclick="openURL('${fullURL}')">
          <div class="ios-banner-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </svg>
          </div>
          <div class="ios-banner-text">
            <div class="ios-banner-domain">${domain}</div>
            <div class="ios-banner-subtitle">Open in browser</div>
          </div>
        </div>
        <button class="ios-banner-close" onclick="dismissBanner(event)" aria-label="Dismiss">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      `;
      
      previewArea.appendChild(banner);
      
      // Animate in
      setTimeout(() => {
        banner.classList.add('ios-banner-visible');
      }, 10);
      
      // Auto-dismiss after 8 seconds (shorter for demo)
      setTimeout(() => {
        dismissBanner();
      }, 8000);
    }

    // Test URL function
    function testURL(url) {
      const domain = extractDomain(url);
      const fullURL = url.startsWith('http') ? url : `https://${url}`;
      
      console.log('Testing URL:', url, '-> Domain:', domain);
      showIOSBanner(domain, fullURL);
      
      // Also generate QR code
      generateQR(url);
    }

    // Open URL
    function openURL(url) {
      console.log('Opening URL:', url);
      window.open(url, '_blank');
      dismissBanner();
    }

    // Dismiss banner
    function dismissBanner(event) {
      if (event) {
        event.stopPropagation(); // Prevent opening URL when clicking X
      }
      
      const banner = document.querySelector('.ios-qr-banner');
      if (banner) {
        banner.classList.remove('ios-banner-visible');
        setTimeout(() => {
          banner.remove();
        }, 300);
      }
    }

    // Clear banner
    function clearBanner() {
      dismissBanner();
    }

    // Generate QR code
    async function generateQR(data) {
      const container = document.getElementById('qr-display');
      container.innerHTML = '';
      
      try {
        const canvas = document.createElement('canvas');
        await QRCode.toCanvas(canvas, data, {
          width: 200,
          margin: 2
        });
        
        const label = document.createElement('div');
        label.textContent = `QR Code for: ${data}`;
        label.style.marginTop = '10px';
        label.style.fontSize = '14px';
        label.style.color = '#666';
        
        container.appendChild(canvas);
        container.appendChild(label);
      } catch (error) {
        console.error('Error generating QR code:', error);
      }
    }

    // Test apple.com on page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        testURL('https://apple.com');
      }, 1000);
    });
  </script>
</body>
</html>
